import { CommonModule, ViewportScroller } from '@angular/common';
import { Component } from '@angular/core';
import { Router, RouterLink, RouterModule } from '@angular/router';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule,RouterLink,RouterModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent {
  showDropdown1: boolean = false;
  showDropdown2: boolean = false;
  isProductMenuOpen: boolean = false;
  isCapabilitiesMenuOpen: boolean = false;
  constructor(private viewportScroller: ViewportScroller, private router: Router) { }


  dataArray: string[] = [
    "Business Process Automation",
    "AI Agents",
    "AI Powered Search",
    "Microsoft Co-Pilot Integration",
    "Custom Application Development",
    "Application Modernization with Cloud-First Approach"
  ];
  private closeTimer: any;

  scrollToSection(sectionId: string) {
    if (this.router.url === '/') {
      // If on the home page, scroll directly
      this.viewportScroller.scrollToAnchor(sectionId);
    } else {
      // If on another page, navigate to the home page first, then scroll
      this.router.navigate(['/']).then(() => {
        this.viewportScroller.scrollToAnchor(sectionId);
      });
    }
  }
  toggleAccountMenu(open: boolean): void {
    this.closeTimer = setTimeout(() => {
      this.isProductMenuOpen = open;
    }, 200);
  }

  toggleCapabilitiesMenu(open: boolean): void {
    this.closeTimer = setTimeout(() => {
      this.isCapabilitiesMenuOpen = open;
    }, 200);
  }

}
