import { CommonModule } from '@angular/common';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatExpansionModule } from '@angular/material/expansion';
import { AiChatMessage, EmailMessage } from '../app.interface';
import { AppService } from '../app.service';
import { HttpClientModule } from '@angular/common/http';
import {
  MatSnackBar,
  MatSnackBarHorizontalPosition,
  MatSnackBarVerticalPosition,
} from '@angular/material/snack-bar';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { Router } from '@angular/router';
import { utimes } from 'fs';

interface Message {
  text: string;
  fromUser: boolean;
}

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [MatExpansionModule, CommonModule, FormsModule, HttpClientModule, MatFormFieldModule, MatSelectModule, MatButtonModule],
  providers: [AppService],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent {
  projectsCompleted: number = 50;
  happyClients: number = 100;
  yearsInBusiness: number = 5;
  awardsWon: number = 3;
  panelOpenState = false;
  public emailMessage = new EmailMessage();
  messages: any;
  messageSent: boolean = false;
  loading: boolean = false;
  newMessage: string = '';
  chatMessages: { sender: string, message: string }[] = [];

  constructor(private appService: AppService, private _snackBar: MatSnackBar, private router: Router) { }


  @ViewChild('chatContainer') private chatContainer: ElementRef;
  ngOnInit(): void {
    this.AiAsk();
  }

  faqs = [
    { question: 'Question 1?', answer: 'Answer to question 1.' },
    { question: 'Question 2?', answer: 'Answer to question 2.' },
  ];
  caseStudies = [
    { title: 'ENERGY SOLUTIONS', description: 'Driving towards the Future of Logistic & Transportation' },
    { title: 'ENERGY SOLUTIONS', description: 'Driving towards the Future of Logistic & Transportation' },
    { title: 'ENERGY SOLUTIONS', description: 'Driving towards the Future of Logistic & Transportation' },
    { title: 'ENERGY SOLUTIONS', description: 'Driving towards the Future of Logistic & Transportation' },
  ];

  capabilities = [
    {
      title: 'Business Process Automation',
      desc: "Streamline your operations with our AI-driven automation solutions. From optimizing workflows to enhancing decision-making, our technology simplifies complex processes, freeing your team to focus on strategic initiatives.",
      link: 'business'
    },
    {
      title: 'AI Agents',
      desc: "Transform customer interactions with our intelligent AI agents. Capable of understanding and responding to user needs in real-time, these agents offer personalized support, ensuring a seamless and satisfying customer experience.",
      link: 'agent'
    },
    {
      title: 'AI Powered Search',
      desc: "Revolutionize your data retrieval with our semantic search solutions. By understanding the meaning behind the text, our systems deliver precise, relevant results, improving information access and decision-making processes.",
      link: 'powered'
    },
    {
      title: 'Microsoft Co-Pilot Integration',
      desc: "Elevate your productivity with Microsoft Co-Pilot. Our integration services streamline your workflows, offering AI-powered assistance that enhances writing, coding, and content creation across your organization.",
      link: 'co-pilot'
    },
    {
      title: 'Custom Application Development',
      desc: "Bring your vision to life with our custom application development services. Tailored to meet your unique needs, our solutions leverage AI to deliver exceptional functionality, user experience, and value.",
      link: 'cu-application'
    },
    {
      title: 'Application Modernization with a Cloud-First Approach',
      desc: "Future-proof your applications with our cloud-first modernization strategy. We reimagine legacy systems with cloud technologies and AI, ensuring scalability, performance, and innovation.",
      link: 'application'
    },
  ]

  resources = [
    { type: 'Blog', title: 'Generative AI in your business', link: 'https://aws.amazon.com/blogs/enterprise-strategy/what-will-generative-ai-mean-for-your-business/', readMore: 'Read the Blog' },
    { type: 'Blog', title: 'Light the way for business transformation', link: 'https://aws.amazon.com/blogs/enterprise-strategy/lessons-from-edisons-bulb-using-generative-ai-to-light-the-way-for-business-transformation/', readMore: "Learn from Edison's bulb" },
    { type: 'Testimonial', title: 'AWS partner community to accelerate innovation', link: 'https://aws.amazon.com/bedrock/testimonials/', readMore: 'Hear from partners' },
    { type: 'Blog', title: 'Introduction to generative AI with AWS leaders', link: 'https://www.allthingsdistributed.com/2023/04/an-introduction-to-generative-ai-with-swami-sivasubramanian.html', readMore: 'Explore the potential' },
    { type: 'Article', title: 'Tips to prepare your organization for generative AI', link: 'https://partners.wsj.com/aws/reinventing-with-the-cloud/how-your-organization-can-prepare-for-generative-ai/', readMore: 'Read Wall Street Journal' },
    { type: 'Video', title: 'Applying generative AI to product development', link: 'https://www.youtube.com/watch?v=LYt0510tNrQ', readMore: "Explore what's possible" }
  ];


  navigateToUrl(url: string) {
    if (url.startsWith('http')) {
      window.open(url, '_blank');
    } else {
      this.router.navigateByUrl(url).then(() => {
        window.scrollTo(0, 0); // Scroll to the top
      });
    }
  }
  teamMembers = [
    {
      name: 'John Doe',
      title: 'Founder/CEO',
      desc: 'Your content goes here. Edit or remove this text inline or in the module Content settings. ',
      image: '../../assets/img/projects/Hero-Background-Image.jpg',
      socials: [
        { icon: 'fa-facebook-f', link: '#' },
        { icon: 'fa-twitter', link: '#' },
      ]
    },
    {
      name: 'John Doe',
      title: 'Founder/CEO',
      desc: 'Your content goes here. Edit or remove this text inline or in the module Content settings. ',
      image: '../../assets/img/projects/Hero-Background-Image.jpg',
      socials: [
        { icon: 'fa-facebook-f', link: '#' },
        { icon: 'fa-twitter', link: '#' },
      ]
    },
    {
      name: 'John Doe',
      title: 'Founder/CEO',
      desc: 'Your content goes here. Edit or remove this text inline or in the module Content settings. ',
      image: '../../assets/img/projects/Hero-Background-Image.jpg',
      socials: [
        { icon: 'fa-facebook-f', link: '#' },
        { icon: 'fa-twitter', link: '#' },
      ]
    },

  ];

  dataArray: string[] = [
    "Business Process Automation",
    "AI Agents",
    "AI Powered Search",
    "Microsoft Co-Pilot Integration",
    "Custom Application Development",
    "Application Modernization with Cloud-First Approach"
  ];

  sendEmailMessage() {
    this.emailMessage.ApplicationName ="Cybotz.ai"
    this.appService.sendMessage(this.emailMessage).subscribe(res => {
      console.log("Email is send");
      // this.emailMessage = new EmailMessage();
      this.openSnackBar()
    })
  }

  openSnackBar() {
    this._snackBar.open('MESSAGE IS SEND', 'Close', {
      horizontalPosition: 'center',
      verticalPosition: 'top',
      duration: 3000,
      panelClass: ['custom-snackbar']
    });

  }


  AiAsk() {
    const storedChatMessages = localStorage.getItem('chatMessages');
    if (storedChatMessages) {
      this.chatMessages = JSON.parse(storedChatMessages);
    } else {
      // Show initial message when the component initializes
      this.chatMessages.push({ sender: 'AI', message: 'Hi there, how can I help you?' });
    }
  }
  sendMessageToAi() {
    this.chatMessages.push({ sender: 'User', message: this.newMessage });
    if (this.newMessage) {
      this.loading = true;
    }
    this.appService.sendAiMessage(this.newMessage).subscribe((res: any) => {
      if (res.message) {
        this.loading = false;
        this.chatMessages.push({ sender: 'AI', message: res.message });
        this.scrollToBottom();
        // Save chat messages to local storage after each message is sent
        localStorage.setItem('chatMessages', JSON.stringify(this.chatMessages));
      }
    });
    this.newMessage = '';
  }

  scrollToBottom(): void {
    setTimeout(() => {
      this.chatContainer.nativeElement.scrollTop = this.chatContainer.nativeElement.scrollHeight;
    }, 100);
  }

  getMessageAlignmentClass(sender: string): string {
    return sender === 'User' ? 'left-align' : 'right-align ai-message';
  }
  refreshChat(): void {
    localStorage.removeItem('chatMessages');
    this.loading = true;


    setTimeout(() => {
      this.loading = false;
      this.chatMessages = [];
      this.AiAsk();
    }, 2000);
  }
}

