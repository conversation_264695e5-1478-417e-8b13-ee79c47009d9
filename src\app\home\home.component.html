<section class="welcome-section">

  <div class="ChatContainer container">

    <div class="welcome-content">
      <h1>Empowering Business through AI:</h1>
      <p class="mb-4">Our mission is to empower businesses with innovative AI automation technologies, driving
        efficiency,
        reducing costs, and enabling a smarter, more connected future.</p>
      <br>
    </div>

    <div class="chat-container">
      <div class="chat-header">
        <h2>AI Agent</h2>
        <button (click)="refreshChat()" class="refresh-button" [disabled]="loading">
          <i class="fas fa-sync-alt"></i>
        </button>
      </div>
      <div class="chat-messages" #chatContainer>
        <div class="message" *ngFor="let message of chatMessages;let i = index">
          <div class="message-contentBox"
            [ngClass]="{'align-left': message.sender === 'AI', 'align-right': message.sender !== 'AI'}">
            <div [ngClass]="getMessageAlignmentClass(message.sender)">
              <!-- <span class="sender">{{ message.sender }}:</span> -->
              <span class="message-body">{{ message.message }}</span>
            </div>
          </div>
        </div>
        <div *ngIf="loading" class="loading-indicator loader">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
      </div>

      <div class="chat-input">
        <input type="text" [(ngModel)]="newMessage" placeholder="Type your message..."
          (keyup.enter)=" newMessage && sendMessageToAi()">
        <button (click)="sendMessageToAi()" [disabled]="loading || !newMessage"><i
            class="fa-solid fa-paper-plane"></i></button>
      </div>

    </div>

  </div>
</section>





<!--
<section class="message-section  container-fluid">
  <div class="container d-flex justify-content-around align-items-center  flex-wrap text-center">
    <div class="message-content ">
      <h1>Call us now <span>+1 234 567</span> or send an <span> youdomain.com</span></h1>
      <p class="text-white">Your content goes here. Edit or remove this text inline or in the module Content settings.
      </p>
    </div>
    <a href="#message" class="custom-button">Contact Us</a>
  </div>
</section>
 -->

<!-- service section   -->
<div class="service-section" id="services">
  <div class="container">
    <!-- <h2>Our Services</h2> -->
    <h1 class="mt-4">Capabilities</h1>
    <p class="text-start peraText">
      Elevate your business to the forefront of the digital age with our cutting-edge AI solutions. <a
        href="https://cybotz.ai/">Cybotz.ai</a>, we
      harness the power of artificial intelligence to unlock unprecedented efficiency, innovation, and growth for your
      business. Our suite of AI capabilities is designed to transform the way you operate, engage, and innovate.

    </p>
    <div class="service-cards">
      @for (item of capabilities; track $index) {
      <div class="service-card" (click)="navigateToUrl(item.link)">
        <!-- <div class="icon">
          <i class="fa-solid fa-shop"></i>
        </div> -->
        <h3>{{item.title}}</h3>
        <p>{{item.desc}}
        </p>
        <div class=" readMoreSection">
          <span class="readMore"> Read More</span>
          <a target="_blank" class="resource-link"><i class="fa-solid fa-arrow-right"></i></a>
        </div>
      </div>
      }
    </div>
    <p class="text-start peraText">At <a href="https://cybotz.ai/">Cybotz.ai</a>, we're not just consultants; we're your
      partners in navigating the AI
      revolution. Connect with us to discover how our AI capabilities can transform your business today and prepare
      you for the opportunities of tomorrow.</p>
  </div>
</div>




<!-- <section class="case-studies" id="studies">
  <div class="container">
    <h2>Case Studies</h2>
    <h1 class="my-4">Case Studies: Business Consulting</h1>
    <div class="row">
      <div class="card" *ngFor="let study of caseStudies">
        <div class="card-content">
          <h3>{{ study.title }}</h3>
          <p>{{ study.description }}</p>
          <a href="#" class="custom-button px-2 py-1 more-Btn">Learn More</a>
        </div>
      </div>
    </div>
  </div>
</section>
 -->

<section class="case-studies" id="studies">
  <div class="container">
    <!-- <h2>Case Studies</h2>
    <h1 class="my-4">Case Studies: Business Consulting</h1> -->
    <div class="row p-3">
      <div class="col-sm-12 col-md-6">
        <h1 class="text-start">Top generative AI use cases</h1>
      </div>
      <div class="col-sm-12 col-md-6">
        <div>
          <mat-accordion>
            <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false" class="my-3">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  Improve customer experiences
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div>
                <h4><strong>Chatbots and virtual assistants</strong></h4>
                <p class="peraText"> Streamline customer self-service processes and reduce operational costs by
                  automating responses for
                  customer service queries through generative AI-powered chatbots, voice bots, and virtual assistants.
                  <a href="https://aws.amazon.com/ai/generative-ai/use-cases/chatbots-and-virtual-assistants/">Learn
                    more</a>
                </p>
              </div>
              <div>
                <h4> Conversational analytics</h4>
                <p>
                  Analyze unstructured customer feedback from surveys, website comments, and call transcripts to
                  identify key topics, detect sentiment, and surface emerging trends. <a
                    href="https://aws.amazon.com/ai/generative-ai/use-cases/conversational-analytics/">Learn more</a>
                </p>
              </div>
              <div>
                <h4>Agent assist</h4>
                <p>
                  Enhance agent performance and improve first contact resolution through task automation, summarization,
                  enhanced knowledge base searches, and tailored cross-sell/upsell product recommendations.<a
                    href="https://aws.amazon.com/ai/generative-ai/use-cases/agent-assist/">Learn more</a>
                </p>
              </div>
              <div>
                <h4> Personalization</h4>
                <p>
                  Deliver better personalized experiences and increase customer engagement with individually curated
                  offerings and communications.<a
                    href="https://aws.amazon.com/ai/generative-ai/use-cases/personalization/">Learn more</a></p>
              </div>
            </mat-expansion-panel>

            <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false" class="my-3">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  Boost employee productivity
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div>
                <h4><strong>Employee assistant</strong></h4>
                <p>Improve employee productivity by quickly and easily finding accurate information, get accurate
                  answers, summarize and create and summarizing content through a conversational interface.
                  <a href="https://aws.amazon.com/q/business/">Learn
                    more</a>
                </p>
              </div>
              <div>
                <h4> Code generation</h4>
                <p>
                  AnalAccelerate application development with code suggestions based on the developer’s comments and
                  code. <a href="https://aws.amazon.com/quicksight/q/">Learn more</a>
                </p>
              </div>
            </mat-expansion-panel>
            <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false" class="my-3">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  Enhance creativity & content creation
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div>
                <h4><strong>Marketing</strong></h4>
                <p>Create engaging marketing content, such as blog posts, social media updates, or email newsletters,
                  saving time and resources.

                </p>
              </div>
              <div>
                <h4>Sales</h4>
                <p>
                  Generate personalized emails, messages based on a prospect's profile and behavior, improving response
                  rates. Generate sales scripts or talking points based on the customer's segment, industry and the
                  product or servic
                </p>
              </div>
              <div>
                <h4>Product development</h4>
                <p>
                  AI can generate multiple design prototypes based on certain inputs and constraints, speeding up the
                  ideation phase, or optimize existing designs based on user feedback and specified constraints.
                </p>
                <a href="https://aws.amazon.com/ai/generative-ai/use-cases/productivity-creativity/">Learn more</a>
              </div>
            </mat-expansion-panel>
            <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false" class="my-3">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  Accelerate process optimization
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div>
                <h4><strong>Document processing</strong></h4>
                <p>Improve business operations by automatically extracting and summarizing data from documents and
                  insights through generative AI-powered question and answering.
                  <a href="https://aws.amazon.com/machine-learning/ml-use-cases/document-processing/">Learn
                    more</a>
                </p>
              </div>
              <div>
                <h4><strong>Data augmentation</strong></h4>
                <p>Generate synthetic data to train ML models, when the original dataset is small, imbalanced or
                  sensitive.
                </p>
              </div>
              <div>
                <h4><strong>Supply chain optimization</strong></h4>
                <p>Improve logistics and reduce costs by evaluating and optimizing different supply chain scenarios.
                  <a href="https://aws.amazon.com/aws-supply-chain/features/">Learn
                    more</a>
                </p>
              </div>
              <div>
                <h4><strong>Employee assistant</strong></h4>
                <p>Improve employee productivity by quickly and easily finding accurate information, get accurate
                  answers, summarize and create and summarizing content through a conversational interface.
                  <a href="https://aws.amazon.com/q/business/">Learn
                    more</a>
                </p>
              </div>
            </mat-expansion-panel>
          </mat-accordion>
        </div>
      </div>
    </div>
  </div>
</section>




<div class="container-fluid">
  <div class="container">
    <h3 class="resources-label">Resource</h3>
    <div class="resources">
      <div class="resource-card" *ngFor="let resource of resources" (click)="navigateToUrl(resource.link)">
        <div>
          <div class="resource-type"><span>{{ resource.type }}</span></div>
          <div class="resource-title">{{ resource.title }}</div>
        </div>
        <div class="d-flex g-2  align-items-center">
          <span class="read"> {{ resource.readMore }}</span>
          <a [href]="resource.link" target="_blank" class="resource-link"><i class="fa-solid fa-arrow-right"></i></a>
        </div>
      </div>
    </div>
  </div>
</div>

<section class="about-section" id="about">
  <h1 class="text-center about-title">About Us:</h1>
  <div class="container">
   <div class="aboutContainer">
    <div class="about-image">
      <img src="../../assets/img/ab_img.png" alt="">
    </div>
    <div class="about-info ">
      <p>At <a href="https://cybotz.ai" class="mx-0">Cybotz.ai</a>, we stand at the forefront of the technological
        revolution, where
        innovation meets intelligence to
        transform the digital landscape of businesses across the globe. At the heart of our mission is the belief that
        artificial intelligence holds the key to unlocking unprecedented levels of efficiency, creativity, and growth.
      </p>

      <p>Founded by a team of visionary tech enthusiasts, we've embarked on a journey to harness the power of AI, making
        it accessible and transformative for businesses in diverse sectors. Our expertise spans a comprehensive suite of
        AI capabilities and products designed to propel your business into the future.
      </p>
      <p>At <a href="https://cybotz.ai/">Cybotz.ai</a>, we're not just about providing AI solutions; we're about
        creating partnerships that foster
        innovation and growth. We believe in working closely with our clients, understanding their challenges and
        aspirations, to develop solutions that not only meet but exceed their expectations.
      </p>
      <p>
        Join us on this journey of transformation. Together, let's unlock the potential of AI and redefine what's
        possible for your business.</p>

    </div>

   </div>


  </div>

</section>



<!-- <section class="success-Cont" id="success">
  <div class="container">
    <h2>Gain a Success With Us!</h2>
    <h1>Doing the right thing, at the right time.</h1>
    <div class="success-section">
      <div class="success-box">
        <h2>{{ projectsCompleted }}+</h2>
        <p>Cases Completed</p>
      </div>
      <div class="success-box">
        <h2>{{ happyClients }}+</h2>
        <p>Awards</p>
      </div>
      <div class="success-box">
        <h2> {{ yearsInBusiness }}%</h2>
        <p>Satisfication</p>
      </div>
      <div class="success-box">
        <h2>{{ awardsWon }}</h2>
        <p>Consultants</p>
      </div>
    </div>
  </div>
</section> -->

<!-- team seciton   -->
<!-- <section id="team">
  <div class="container">
    <div class="row">
      <div class=" col-12 text-center">
        <h2 class="section-heading text-uppercase">Our Team</h2>
        <h3 class="section-subheading text-muted">Meet the Professionals Behind Our Success</h3>
      </div>
    </div>
    <div class="row">
      <div class=" col-sm-12 col-lg-3 border m-1 zoom" *ngFor="let member of teamMembers">
        <div class="team-member">
          <img class="rounded-circle" [src]="member.image" [alt]="member.name">
          <h4>{{ member.title }}</h4>
          <p class="text-muted">{{ member.name }}</p>
          <h3 class="text-muted">{{ member.desc }}</h3>
          <ul class="social-buttons">
            <li *ngFor="let social of member.socials">
              <a [href]="social.link">
                <i class="fab {{ social.icon }}"></i>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="d-flex justify-content-center  ">
      <div class="d-flex align-items-center newSection justify-content-center shadow px-2 my-5 rounded-5">
        <button class="btn btn-primary mr-2 shadow custom-button py-1">New</button>
        <p class="mr-2 mt-3 mx-2">We are Hiring. <a href="" class="text-decoration-none">View Open Positions</a></p>

      </div>
    </div>

  </div>
</section> -->

<!-- <section class="consultant-section">
  <div class="overlay"></div>
  <div class="container">
    <h2 class="section-header">Need a Consultant?</h2>
    <p class="section-text">You can also style every aspect of this content in the module Design settings and even apply
      custom CSS to this text in the module Advanced settings.</p>
    <a href="#" class="custom-button">GET STARTED</a>
  </div>
</section> -->


<!-- out blogs  sectionas   -->
<!-- <section class="blog-section" id="blogs">
  <div class="container">
    <h3 class="mian-title">Our Blogs</h3>
    <h2 class="main-header">Read Our Latest Articles, Tips & News</h2>
    <div class="blogs-container">
      <div class="blog-cards">
        <div class="blog-card">
          <div class="blog-thumbnail">
            <img src="../../assets/img/projects/3.jpg" alt="Blog 1 Thumbnail">
            <div class="date">SEP 26, 2019</div>
          </div>
          <div class="blog-content">
            <h1>Post 1</h1>
            <h3 class="blog-title">Uncategorized</h3>
            <p class="blog-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut pretium pretium tempor. Ut
              eget imperdiet neque. In volutpat ante semper diam.</p>
            <a href="#" class="read-more-button">Read More</a>
          </div>
        </div>
      </div>


      <div class="blog-cards ">
        <div class="blog-card">
          <div class="blog-thumbnail">
            <img src="../../assets/img/projects/1.jpg" alt="Blog 1 Thumbnail">
            <div class="date">SEP 26, 2019</div>
          </div>
          <div class="blog-content">
            <h1>Post 1</h1>
            <h3 class="blog-title">Uncategorized</h3>
            <p class="blog-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut pretium pretium tempor. Ut
              eget imperdiet neque. In volutpat ante semper diam.</p>
            <a href="#" class="read-more-button">Read More</a>
          </div>
        </div>
      </div>

      <div class="blog-cards">
        <div class="blog-card">
          <div class="blog-thumbnail">
            <img src="../../assets/img/projects/2.jpg" alt="Blog 1 Thumbnail">
            <div class="date">SEP 26, 2019</div>
          </div>
          <div class="blog-content">
            <h1>Post 1</h1>
            <h3 class="blog-title">Uncategorized</h3>
            <p class="blog-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut pretium pretium tempor. Ut
              eget imperdiet neque. In volutpat ante semper diam.</p>
            <a href="#" class="read-more-button">Read More</a>
          </div>
        </div>
      </div>


    </div>

  </div>
</section>
 -->

<section class="message-sections" id="contact">
  <div class="message-containers container">

    <div class="message-details">
      <h3>Are you ready to take your business to the next level?</h3>
      <p>Founded by a team of visionary tech enthusiasts, we've embarked on a journey to harness the power of AI, making
        it accessible and transformative for businesses in diverse sectors. Our expertise spans a comprehensive suite of
        AI capabilities and products designed to propel your business into the future.
      </p>
      <!-- <span class="address-info">
        <p><i class="fa-solid fa-location-dot"></i></p>
        <div>
          <p class="adress">Address</p>
          <p class="content-text">Los Angeles Orange <br> California</p>
        </div>
      </span> -->
      <!--
      <span class="address-info">
        <p><i class="fa-regular fa-envelope"></i></p>
        <div>
          <p class="adress">Mail or Call</p>
          <p class="content-text">Los Angeles Orange <br> California</p>
        </div>
      </span> -->
      <!-- <span class="address-info">
        <p><i class="fa-solid fa-phone"></i></p>
        <div>
          <p class="adress">Phone</p>
          <p class="content-text"> ************</p>
        </div>
      </span> -->
      <!-- <div class="social-media-icons">
        <a href="#"><i class="fa-brands fa-facebook-f"></i></a>
        <a href="#"> <i class="fa-brands fa-twitter"></i></a>
        <a href="#"><i class="fa-brands fa-pinterest-p"></i></a>
        <a href="#"><i class="fa-brands fa-youtube"></i></a>
      </div> -->
    </div>

    <div class="send-message">
      <h3>Send a message right now</h3>
      <form #messageForm="ngForm" (ngSubmit)="sendEmailMessage()">
        <div class="form-group">
          <input type="text" placeholder="Name" class="full-width" [(ngModel)]="emailMessage.Name" name="name" required>
          <br>
          <div
            *ngIf="messageForm.controls['name']?.invalid && (messageForm.controls['name'].dirty || messageForm.controls['name'].touched)"
            class="error-message">
            Name is required.
          </div>
        </div>
        <div class="form-group HalfCont">
          <div class="half-cont">
            <input type="email" placeholder="Email Address" class="half-width" [(ngModel)]="emailMessage.Email"
              name="email" required email>
            <div
              *ngIf="messageForm.controls['email']?.invalid && (messageForm.controls['email'].dirty || messageForm.controls['email'].touched)"
              class="error-message">
              Please enter a valid email address.
            </div>
          </div>

          <div class="half-cont">
            <input type="tel" placeholder="Phone Number" class="half-width phone-input"
              [(ngModel)]="emailMessage.PhoneNumber" name="phone">
            <div
              *ngIf="messageForm.controls['phone']?.invalid && (messageForm.controls['phone'].dirty || messageForm.controls['phone'].touched)"
              class="error-message">
              Please enter a valid 10-digit phone number.
            </div>
          </div>

        </div>
        <div class="form-group">
          <textarea placeholder="Message" [(ngModel)]="emailMessage.Message" name="message" required></textarea>
          <div
            *ngIf="messageForm.controls['message']?.invalid && (messageForm.controls['message'].dirty || messageForm.controls['message'].touched)"
            class="error-message">
            Message is required.
          </div>
        </div>
        <button type="submit" class="custom-button" [disabled]="messageForm.invalid">Send My Message</button>
      </form>
    </div>

  </div>
</section>


<!-- faqs Section -->
<!-- <div class="faq-section" id="faq">
  <div class="container">
    <h2 class="faq-title">Frequently Asked Questions</h2>
    <div class="faq-header">Answer to some common Questions</div>

    <div class="faq-Cont">
      <div class="faq-question">
        <mat-accordion>
          <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false" class="my-3">
            <mat-expansion-panel-header>
              <mat-panel-title>
                YOUR TITLE GOES HERE
              </mat-panel-title>
            </mat-expansion-panel-header>
            <p>Your content goes here. Edit or remove this text inline or in the module Content settings. You can also
              style
              every aspect of this content in the module Design settings and even apply custom CSS to this text in the
              module
              Advanced settings.</p>
          </mat-expansion-panel>

          <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false" class="my-3">
            <mat-expansion-panel-header>
              <mat-panel-title>
                YOUR TITLE GOES HERE
              </mat-panel-title>
            </mat-expansion-panel-header>
            <p>Your content goes here. Edit or remove this text inline or in the module Content settings. You can also
              style
              every aspect of this content in the module Design settings and even apply custom CSS to this text in the
              module
              Advanced settings.</p>
          </mat-expansion-panel>


          <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false" class="my-3">
            <mat-expansion-panel-header>
              <mat-panel-title>
                YOUR TITLE GOES HERE
              </mat-panel-title>
            </mat-expansion-panel-header>
            <p>Your content goes here. Edit or remove this text inline or in the module Content settings. You can also
              style
              every aspect of this content in the module Design settings and even apply custom CSS to this text in the
              module
              Advanced settings.</p>
          </mat-expansion-panel>



          <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false" class="my-3">
            <mat-expansion-panel-header>
              <mat-panel-title>
                YOUR TITLE GOES HERE
              </mat-panel-title>
            </mat-expansion-panel-header>
            <p>Your content goes here. Edit or remove this text inline or in the module Content settings. You can also
              style
              every aspect of this content in the module Design settings and even apply custom CSS to this text in the
              module
              Advanced settings.</p>
          </mat-expansion-panel>

        </mat-accordion>
        <div>
        <button class="custom-button">SEND A MESSAGE</button>
        <a href="tel:+1234567890" class="plain-text-link text-white"><i class="fa-solid fa-phone"></i> ************</a>
      </div>
      </div>
      <div class="image-card">
        <img src="../../assets/img/projects/Man-Image-480x320.png" alt="Image">
      </div>
    </div>
  </div>
</div>
 -->
