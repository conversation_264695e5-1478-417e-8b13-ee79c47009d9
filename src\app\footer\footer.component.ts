import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.css'
})
export class FooterComponent {

  // dataArray: string[] = [
  //   "Business Process Automation",
  //   "AI Agents",
  //   "AI Powered Search",
  //   "Microsoft Co-Pilot Integration",
  //   "Custom Application Development",
  //   "Application Modernization with Cloud-First Approach"
  // ];

  dataArray: { title: string, link: string }[] = [
    { title: "Business Process Automation", link: "business" },
    { title: "AI Agents", link: "agent" },
    { title: "AI Powered Search", link: "powered" },
    { title: "Microsoft Co-Pilot Integration", link: "co-pilot" },
    { title: "Custom Application Development", link: "cu-application" },
    { title: "Application Modernization with Cloud-First Approach", link: "application" }
  ];


  constructor(private router: Router) { }

  navigateToUrl(url: string) {
    if (url.startsWith('http')) {
      window.open(url, '_blank');
    } else {
      this.router.navigateByUrl(url).then(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      });
    }
  }
}
