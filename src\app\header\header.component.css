/* Header styles */
.container {
  max-width: 1200px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 30px;
  background-color: #ffff;
}

.logo {
  width: 200px;
  height: auto;
}

.navbar-brand {
  padding: 0;
  margin: 0;
}

.navigation {
  display: flex;
  align-items: center;
  gap: 30px;
}

.navigation-item {
  margin-right: 20px;
  color: #07b98a !important;
  text-decoration: none;
}

.navigation-item:last-child {
  margin-right: 0;
}

.dropdown {
  position: relative;
}

.dropdown-lable {
  color: #07b98a;
  font-size: 16px;
  /* font-weight: 500; */
}

.dropdown-menu {
  z-index: 22222;
}

.dropdown-content {
  margin-top: 20px;
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 140;
}

.dropdown-content a {
  color: black;
  padding: 4px 16px;
  text-decoration: none;
  display: block;
}

.dropdown-content a:hover {
  background-color: #f1f1f1;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.main-Button {
  background-color: var(--dark4-blue-color);
  color: black;
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
}


.navbar {
  /* background-color: var(--dark1-blue-color) !important; */
  background-color: var(--text-color) !important;
}

.nav-link {
  color: 442f91;
  font-size: large;
  /* font-weight: 500; */
}

.nav-link:hover {
  text-decoration: underline;
  opacity: 0.7;

}

.active {
  color: #371d97;
}


.account-menu {
  position: absolute;
  top: 90%;
  width: 150px;
  left: -40px;
  background-color: var(--grey5-color);
  border: 1px solid hsl(0, 27%, 94%);
  padding: 10px 0px;
  z-index: 1565;
  border-radius: 5px;
}

.side-menu {
  left: -80px;
  width: 250px;
}

.menu-button {
  text-align: start;
  display: block;
  width: 100%;
  padding: 8px 10px;
  margin-bottom: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  text-decoration: none;
  color: black;
  font-weight: 500;
}

.menu-button:hover {
  background-color: #e0e0e0;
  text-decoration: underline;
}

@media only screen and (max-width: 992px) {
  .account-menu{
    left: 0 !important;
    width: 250px;
  }


}
