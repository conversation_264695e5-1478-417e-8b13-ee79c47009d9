

/* footer section */
footer {
  position: relative;
  background-color: var(--grey5-color);
  background-size: cover;
  padding: 20px;
  padding-top: 60px;
  padding-bottom: 20px;
}

.footer-content {
  display: flex;
  gap: 20px;
  justify-content: start;
  flex-wrap: wrap;
}

.footer-contact .address-info .adress {
  color: var(--black-color);
}

.footer-contact .address-info .content-text {
  color: var(--black-color);
}

.footer-logo img {
  width: 250px;
  padding: 0px 0px;
  margin-bottom: 10px;
}

.footer-nav,
.footer-contact {
  /* flex: 1; */
  margin-bottom: 20px;
}

.footer-nav h3 {
  font-weight: 700;
  font-size: 29px;
  color: var(--black-color);
}

.footer-contact h3 {
  font-weight: 700;
  font-size: 29px;
  color: var(--black-color);
}

.footer-contact p {
  font-weight: 700;
}

.footer-nav {
  width: 300px;
}

.footer-nav ul {
  list-style: none;
  padding: 0;
  text-align: start;

}

.footer-nav ul li {
  margin-bottom: 10px;
}

.footer-nav ul li a {
  color: var(--black-color);
  text-decoration: underline;
  letter-spacing: 1px;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: 0.05rem;
  text-decoration: none;
  margin-bottom: 20px;

}

.footer-nav ul li a:hover {
  text-decoration: underline;
  background-color: #e0e0e0;
}

.copyright-Section {
  text-align: center;
  font-family: Open Sans, Arial, sans-serif;
  font-size: 14px;
  color: var(--black-color);
  line-height: 1.7em;
  font-weight: 500;
  -webkit-font-smoothing: antialiased;
  font-size: 1.25rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;

}
