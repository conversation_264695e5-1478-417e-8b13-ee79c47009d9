{"name": "samara<PERSON>", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:samaraj": "node dist/samaraj/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/cdk": "^17.2.1", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/material": "^17.2.1", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/platform-server": "^17.0.0", "@angular/router": "^17.0.0", "@angular/ssr": "^17.0.10", "@fortawesome/fontawesome-free": "^6.5.1", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "express": "^4.18.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.10", "@angular/cli": "^17.0.10", "@angular/compiler-cli": "^17.0.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.2"}}