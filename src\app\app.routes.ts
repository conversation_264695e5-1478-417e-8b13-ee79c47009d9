import { Routes } from '@angular/router';
import { AiAgentComponent } from './Capabilities/ai-agent/ai-agent.component';
import { AiPoweredComponent } from './Capabilities/ai-powered/ai-powered.component';
import { ApplicationModernizationComponent } from './Capabilities/application-modernization/application-modernization.component';
import { BusinessProcessComponent } from './Capabilities/business-process/business-process.component';
import { CustomApplicationComponent } from './Capabilities/custom-application/custom-application.component';
import { MicrosoftCoPilotComponent } from './Capabilities/microsoft-co-pilot/microsoft-co-pilot.component';
import { HomeComponent } from './home/<USER>';

export const routes: Routes = [
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  { path: 'home', component: HomeComponent },
  { path: 'agent', component: AiAgentComponent },
  { path: 'powered', component: AiPoweredComponent },
  { path: 'application', component: ApplicationModernizationComponent },
  { path: 'business', component: BusinessProcessComponent },
  { path: 'cu-application', component: CustomApplicationComponent },
  { path: 'co-pilot', component: MicrosoftCoPilotComponent },
];
