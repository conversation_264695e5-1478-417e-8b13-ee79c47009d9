import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, tap } from 'rxjs';
import { AiChatMessage, EmailMessage } from './app.interface';

@Injectable({
  providedIn: 'root'
})
export class AppService {
  // private apiUrl = 'https://localhost:7176/api/EmailSend/SendMessage';
  private apiUrl: string;
  private chatApi = 'https://genai.cybotz.ai/api/GenAI/CybotzCustomerService';
  private sessionId: string | null = null; // This will store the sessionId

  constructor(private http: HttpClient) {
    const isLocal = window.location.hostname === 'localhost';
    this.apiUrl = isLocal
      ? 'https://localhost:7176/api/EmailSend/SendMessage'
      : 'https://www.api.3dbotics.com/api/EmailSend/SendMessage';
  }

  sendMessage(emailMessage: EmailMessage): Observable<string> {
    return this.http.post(this.apiUrl, emailMessage, { responseType: 'text' });
  }

  sendAiMessage(description: string): Observable<ResponseMessage> {
    const chatRequest: ChatRequest = {
      sessionId: this.sessionId, // Use the stored sessionId if available
      query: description
    };


    return this.http.post<ResponseMessage>(this.chatApi, chatRequest).pipe(
      tap(response => {
        if (!this.sessionId && response.sessionId) {
          this.sessionId = response.sessionId;
        }
      })
    );
  }
}

interface ResponseMessage {
  isError: boolean;
  message: string;
  sessionId: string;
}

interface ChatRequest {
  sessionId: string;
  query: string;
}

