<nav class="navbar navbar-expand-lg consultant-section ">
  <div class="container">
    <a class="navbar-brand" href="/">
      <img src="../../assets/img/projects/logo.jpg" alt="Logo" class="logo">
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
      aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
        <li class="nav-item position-relative" (mouseenter)="toggleAccountMenu(true)"
          (mouseleave)="toggleAccountMenu(false)">
          <a class="nav-link active" href="#">Products</a>

          <div *ngIf="isProductMenuOpen" class="account-menu" (mouseenter)="cancelClose()" (mouseleave)="closeMenu()">
            <a class="menu-button">SkillPilot</a>
            <a class="menu-button">BrowseBot</a>
          </div>

        </li>


        <li class="nav-item position-relative" (mouseenter)="toggleCapabilitiesMenu(true)"
          (mouseleave)="toggleCapabilitiesMenu(false)">
          <a class="nav-link " href="#services" aria-disabled="true">Capabilities</a>

          <div *ngIf="isCapabilitiesMenuOpen" class="account-menu side-menu" (mouseenter)="cancelClose()"
            (mouseleave)="closeMenu()">
            <ng-container>
              <a class="menu-button" [routerLink]="['/business']">Business Process Automation</a>
              <a class="menu-button" [routerLink]="['/agent']">AI Agents</a>
              <a class="menu-button" [routerLink]="['/powered']">AI Powered Search</a>
              <a class="menu-button" [routerLink]="['/co-pilot']">Microsoft Co-Pilot Integration</a>
              <a class="menu-button" [routerLink]="['/cu-application']">Custom Application Development</a>
              <a class="menu-button" [routerLink]="['/application']">Application Modernization with Cloud-First
                Approach</a>
            </ng-container>
          </div>

        </li>

        <li class="nav-item">
          <a class="nav-link"  (click)="scrollToSection('about')">About</a>
        </li>
        <!-- <li class="nav-item">
          <a class="nav-link" href="#blogs">Blogs </a>
        </li> -->

        <li class="nav-item">
          <a class="nav-link" (click)="scrollToSection('contact')">Contact</a>
        </li>
      </ul>
      <!-- <form class="d-flex" role="search">
        <button class=" main-Button" type="submit">Back to Main Site</button>
      </form> -->
    </div>
  </div>
</nav>
