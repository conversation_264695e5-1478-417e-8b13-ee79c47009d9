/* custom-snackbar class to style the snackbar */
::ng-deep .mdc-snackbar__surface.ng-tns-c3412835194-8 {
  background: #195b8f !important;
  border-radius: 8px;
  color: white !important;
}

::ng-deep .mat-mdc-snack-bar-label.mdc-snackbar__label {
  border-radius: 8px;
  color: white !important;
}

.welcome-section {
  position: relative;
  background-image: url('../../assets/img/projects/Background3.jpg');
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  align-items: flex-start;
  text-align: center;
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.ChatContainer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.welcome-content {
  color: var(--text-color);
  padding: 20px;
  z-index: 2;
  width: 100%;

}

.welcome-content {
  color: white;
  padding: 20px;
  max-width: 1200px;
  text-align: left;
}

.welcome-content h3 {
  font-size: 2em;
  padding-bottom: 5px;
  line-height: 1em;
  font-weight: 500;
}

.welcome-content h1 {
  font-size: 3em;
  margin-bottom: 10px;
  line-height: 1em;
  font-weight: 500;

}

.welcome-content p {
  font-size: 1.25em;
  font-family: Open Sans, Arial, sans-serif;
  line-height: 1.7em;
  font-weight: 500;
  letter-spacing: 0.05em;
  /* max-width: 50%; */
}

/* chat box section  */

.chat-container {
  width: 100%;
  margin: 50px 0px;

  border: 1px solid #ccc;
  background-color: #fff;
  border-radius: 5px;
  overflow: hidden;
  z-index: 66;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: var(--dark-blue-color);
}

.chat-header h2 {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
}

.refresh-button {
  background-color: var(--text-color);
  color: var(--black-color);
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.refresh-button:hover {
  background-color: #ccc;

}

.refresh-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.chat-messages {
  padding: 8px 10px 0px 10px;
  /* max-height: 300px; */
  height: 450px;
  overflow-y: auto;
}

.message {
  /* background-color: var(--grey5-color); */
  /* padding: 4px 8px; */
  border-radius: 5px;
  /* margin-bottom: 2px; */
}

.message {
  /* margin-bottom: 10px; */
  text-align: start;

}

.message-contentBox {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.ai-message {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
}

.align-left {
  align-items: flex-start;
}

.align-right {
  align-items: flex-end;
  float: right;
}

.sender {
  text-align: start;
  font-weight: bold;
}

.message-body {
  background-color: #f0f0f0;
  display: inline-block;
  text-align: left;
  padding: 8px 12px;
  border-radius: 8px;
  background-color: #f0f0f0;
  line-height: 1.75rem;
}


.loading-indicator {
  padding: 5px 10px;
  color: var(--black-color);
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  z-index: 9999;
}

.left-align {
  text-align: left;
  /* Align messages from User to the left */
}

.right-align {
  text-align: right;
  /* Align messages from AI to the right */
}

.message.user .message-body {
  background-color: #cfe2ff;
}

.message.ai .message-body {
  background-color: #f0f0f0;
}


.chat-input {
  display: flex;
  padding: 10px;
}

.chat-input input {
  flex: 1;
  padding: 8px;
  border-radius: 5px;
  border: 1px solid #ccc;
}

.chat-input button {
  padding: 8px 16px;
  border: none;
  background-color: #007bff;
  color: #fff;
  border-radius: 5px;
  cursor: pointer;
  margin: 5px;
  margin-left: -55px;

}

.chat-input button:disabled {
  background-color: #ccc;
  color: #666;
  cursor: not-allowed;
}

/* loading amination  */
.loading-indicator.loader {
  /* border: 1px solid black; */
  display: flex;
  justify-content: start;
  align-items: center;
  width: max-content;
}

.dot {
  width: 10px;
  height: 10px;
  background-color: #007bff;
  border-radius: 50%;
  margin: 0 5px;
  animation: bounce 1.4s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {

  0%,
  100% {
    transform: scale(0);
  }

  50% {
    transform: scale(1);
  }
}


/* message seciton  */

.message-section {
  padding: 54px 0 45px 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  background-color: var(--dark-blue-color);
  text-align: center;
}

.message-content {
  max-width: 1200px;
  padding: 0px 10px;
  margin-bottom: 0px;
}

.message-content h1 {
  color: var(--text-color);
  font-size: 3em;
  margin-bottom: 10px;
  line-height: 1em;
  font-weight: 500;
}

.message-content span {
  color: #ADFFFF;
}

.message-content p {
  color: black;
  font-size: 1.25rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;
}

.message-content h1 {
  font-size: 2.5em;
  font-weight: 500;
  margin-bottom: 10px;
}

.message-content p {
  font-size: 1.2em;
}

.custom-button {
  text-decoration: none;
  color: black;
  background-color: white;
  padding: 15px 40px;
  border: 2px solid var(--dark-blue-color);
  /* border-radius: 30px; */
  font-size: 1.2em;
  cursor: pointer;
  transition: all 0.3s ease;
}

.custom-button:hover {
  color: white;
  background-color: var(--dark3-blue-color);
  /* background-color:  var(--dark4-blue-color); */
  box-shadow: 0px 2px 4px var(--dark4-blue-color);
}




/* service page */
.service-section {
  /* background-color: var(--grey5-color); */
  text-align: center;
  padding: 20px 0px;
}

.service-section h2 {
  margin-top: 15px;
  color: #8f8989;
  font-size: 1rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;


}

.service-section .peraText {
  font-family: 'Allerta', Helvetica, Arial, Lucida, sans-serif;
  color: #666;
  margin-bottom: 35px;
  font-size: 1.2rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;
  text-align: start;
}

.service-section h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 3em;
  line-height: 1em;
  font-weight: 500;
}

.service-section p {
  color: #666;
  margin-bottom: 35px;
  font-size: 1rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;
}

.service-cards {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  transition: transform 0.3s ease;
  margin-bottom: 40px;
  gap: 20px;
}

.service-card {
  width: 350px;
  padding: 15px;
  margin: 0px 10px;
  border-radius: 5px;
  background-color: #eff2f6;
  border-radius: 24px !important;
  text-decoration: none !important;
  height: 350px;
  position: relative;

}

.service-card:hover {
  cursor: pointer;
  box-shadow: 0 10px 20px -10px rgba(214, 0, 186, 0.8), 0 10px 20px -10px rgba(104, 66, 255, 0.8), 0 10px 20px -10px rgba(0, 126, 148, 0.8);
  transform: scale(1.02);
}

.readMoreSection {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 5px;
  bottom: 15px;
  left: 30px;
}

.service-card .readMore {
  display: none;
  transition: opacity 0.5s ease;
  font-weight: 500;
  color: #141f2e;
  padding: 5px;
}

.service-card:hover .readMore {
  display: block;
}
.service-card:hover .icon i {
  background: #5b5f97;
  box-shadow: 0px 10px 50px rgba(0, 0, 0, 0.28);
  transform: scale(1.10);
  color: white;
}
.service-card:hover button {
  background: #5b5f97;
  color: white;
  background-color: #0056b3;
  /* border-radius: 100px; */
  color: rgb(160, 157, 157);
}

.service-card .icon {
  font-size: 24px;
  margin-bottom: 15px;
}

.service-card .icon i {
  font-size: 30px;
  border: 2px solid #0056b3;
  border-radius: 50%;
  padding: 25px;
}

.service-card h3 {
  margin-top: 10px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 700;
  font-size: 25px;
}

.service-card p {
  font-family: 'Allerta', Helvetica, Arial, Lucida, sans-serif;
  color: #666;
  padding: 0 20px;
  font-size: 1rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;
  text-align: start;
  margin-bottom: 20px;
}

.service-card button {
  color: #0056b3;
  background-color: white;
  border: none;
  padding: 8px 16px;
  /* border-radius: 5px; */
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.service-card button i {
  font-size: 10px;
  margin-left: 2px;
}


/* faq section  */

.faq-section {
  padding: 40px 20px;
  padding-bottom: 0px;
  /* background-color: var(--dark-blue-color); */
}

.faq-title {
  text-align: center;
  color: var(--black-color);
  font-size: 2em;
  padding-bottom: 5px;
  line-height: 1em;
  font-weight: 500;
}

.faq-header {
  text-align: center;
  color: var(--black-color);
  font-size: 3em;
  margin-bottom: 10px;
  line-height: 1em;
  font-weight: 500;
}

.faq-Cont {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;

}

.faq-question {
  /* margin-bottom: 10px; */
  width: 50%;
}

.image-card {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.image-card img {
  max-width: 100%;
  max-height: 100%;
}


.plain-text-link {
  font-size: 15px;
  text-decoration: none;
  margin: auto 30px;
  font-weight: 500;
}

.plain-text-link:hover {
  cursor: pointer;
}


/* case section  */

.case-studies {
  background-color: var(--dark-blue-color);
  padding: 50px 0;
}

.container {
  max-width: 1200px;
}

.case-studies h2 {
  text-align: center;
  font-size: 2em;
  line-height: 1em;
  font-weight: 500;
}

.case-studies h1 {
  text-align: center;
  color: white;
  line-height: 2.5rem;
  font-size: 3em;
  line-height: 1em;
  font-weight: 500;
}

.mat-expansion-panel:not([class*=mat-elevation-z]) {
  border-bottom: 1px solid;
  box-shadow: none;
}

.mat-panel-title.mat-expansion-panel-header-title {}

.mat-expansion-panel-header-title,
.mat-expansion-panel-header-description {
  font-size: 1.2rem;

}

.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover {
  background-image: linear-gradient(96deg, #d1fbff80, #75cfff80 29.13%, #978aff80 70.56%);
}

.mat-expansion-panel-header.mat-expanded:focus,
.mat-expansion-panel-header.mat-expanded:hover {
  background-image: linear-gradient(96deg, #d1fbff80, #75cfff80 29.13%, #978aff80 70.56%);
}

#productDetailsAccordation .accordion-button.collapsed::after {
  content: "\f067";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  background: none;
}

#productDetailsAccordation .accordion-button:not(.collapsed)::after {
  content: "\f068";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  background: none;
}



mat-expansion-panel p {
  font-family: 'Allerta', Helvetica, Arial, Lucida, sans-serif;
  color: #666;
  padding: 0 22px;
  font-size: 1rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;
  text-align: start;
  margin-bottom: 20px;
}

mat-expansion-panel h4 {
  letter-spacing: .03125em;
  margin: 0 0 16px;
  font-weight: bold;

}

.resources-label {
  font-size: 2.8rem;
  line-height: 3.6rem;
  font-weight: 500;
  color: #141f2e;
  /* margin-bottom: 25px; */
  margin-top: 45px;
  line-height: 2.5rem;
  font-size: 3em;
  text-align: center;
}

.resources {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: space-between;
  padding: 30px 0 50px 0;

}

.resource-card {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  background-color: #eff2f6;
  border-radius: 24px !important;
  text-decoration: none !important;
  padding: 28px 28px 20px;
  cursor: pointer;
  width: 32%;
  height: 300px;
}

.resource-card:hover {
  box-shadow: 0 10px 20px -10px rgba(214, 0, 186, 0.8), 0 10px 20px -10px rgba(104, 66, 255, 0.8), 0 10px 20px -10px rgba(0, 126, 148, 0.8);
}

.resource-type span {
  padding: 4px 8px;
  border-radius: 4px;
  background-image: linear-gradient(120deg, #ff94f180 7.63%, #978aff80 37.94%, #00d2e580 65.23%, #8ffff880 92.12%);
  font-size: 1rem !important;
  line-height: 2rem !important;
  font-weight: 500;
  color: #141f2e;
}

.resource-title {
  margin-top: 20px;
  font-size: 1.5rem;
  line-height: 2.2rem;
  font-weight: 500;
  font-family: Amazon Ember Display, Helvetica Neue, Helvetica, Arial, sans-serif;
}

.resource-card:hover .read {
  display: block;
}

.resource-card:hover .resource-type span {
  background-image: linear-gradient(0deg, #ff94f180 7.63%, #978aff80 37.94%, #00d2e580 65.23%, #8ffff880 92.12%);
}

.read {
  display: none;
  transition: opacity 0.5s ease;
  font-weight: 500;
  color: #141f2e;
  padding: 5px;
}

.resource-link {
  font-size: 1.5em;
  color: #050505;
  text-decoration: none;
}

.row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 1px;
}

.card {
  width: calc(50% - 15px);
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
}

.card:hover .custom-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 100%;
  background-color: #195b8f;
}

.custom-button:disabled {
  background-color: #ccc;
  color: #666;
  cursor: not-allowed;
}


.card-content h3 {
  margin-bottom: 5px;
  color: rgba(0, 0, 0, 0.56);
  font-size: 1.25rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;


}

.card-content p {
  font-size: 20px;
  margin-bottom: 10px;
  font-weight: 700;
  font-size: 2em;
  padding-bottom: 5px;
  line-height: 1em;
  font-weight: 500;
}

.more-Btn {
  align-self: flex-end;
  margin-top: auto;
  color: #5b5f97;
  border-width: 2px;
  /* border-radius: 100px; */
  letter-spacing: 0px;
  font-size: 10px;
  font-weight: 700;
  background-color: rgba(0, 0, 0, 0);
}

/* about secitons  */

.about-section {
  background-color: var(--dark-blue-color);
  padding: 55px 0px;
}


.about-info {
  /* width: 100%; */
  /* display: flex;
  justify-content: stretch; */
  /* gap: 20px; */
}

.aboutContainer{
  display: flex;
  align-items: center;
}
.about-info p {
  /* padding: 5px 0; */
  /* border-bottom: 2px solid var(--grey5-color); */
  padding-bottom: 5px;
}

/* .about-info p:first-of-type {
  padding-top: 25px;
}


.about-info p:nth-child(even) {
  margin-left: 18%;
}

.about-info p:nth-child(odd) {
  margin-right: 18%;
} */

.about-info p a {
  padding: 0px;
  color: var(--dark4-blue-color);

}

.about-title {
  line-height: 3rem;
  font-weight: 700;
  font-size: 41px;
  color: var(--text-color);
}

.about-info p {
  /* font-weight: 500; */
  font-size: 1.25rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;
  color: var(--text-color);
}

.about-image {

}

.about-image img {
  max-width: 100%;
  height: 100%;
  width: 100%;
}


/* successsections  */
.success-Cont {
  background-color: var(--grey5-color);
  padding: 60px 20px;

}

.success-Cont {
  position: relative;
  /* background-image: url('../../assets/img/projects/Hero-Background-Image.jpg'); */
  background-color: var(--grey5-color);

  background-size: cover;
  background-position: center;
}

.success-Cont::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.success-Cont h2 {
  text-align: center;
  color: var(--black-color);
  z-index: 2;
  font-size: 2em;
  line-height: 1em;
  font-weight: 500;
}

.success-Cont h1 {
  text-align: center;
  font-weight: 700;
  color: var(--black-color);
  z-index: 2;

  font-size: 3em;
  margin-bottom: 10px;
  line-height: 1em;
  font-weight: 500;
  padding-bottom: 20px;
}

.success-section {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.success-box {
  width: 200px;
  text-align: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.582);
}

.success-box h2 {
  font-size: 72px;
  line-height: 72px;
  font-weight: 500;
  color: var(--dark-blue-color);
}

.success-box p {
  color: rgba(0, 0, 0, 0.68)
}


/* team seciton  */
#team {
  padding: 50px 0px;
  background-color: var(--dark-blue-color);
}

.team-member {
  margin: 10px;
}

.section-heading {
  color: var(--text-color) !important;
}

.section-subheading {
  font-weight: 700;
  font-size: 41px;
  color: var(--text-color) !important;
  margin-bottom: 20px;
  line-height: 3rem;
}

.zoom {
  transition: transform 0.3s ease;
  background-color: var(--grey5-color);
}

.zoom:hover {
  transform: scale(1.1);
}

.team-member img {
  width: 73px;
  height: 73px;
  border-radius: 50%;
  margin-bottom: 16px;

}

.team-member h4 {
  margin-bottom: 2px;
  font-size: 12px;
}

.team-member p {
  font-size: 25px;
  color: #888;
  font-family: Open Sans, Arial, sans-serif;
  color: #333;
  line-height: 1em;
  font-weight: 500;
}

.newSection {
  background-color: var(--grey5-color);
}

.social-buttons {
  list-style-type: none;
  padding: 0;
}

.social-buttons li {
  display: inline-block;
  margin-right: 10px;
}

.social-buttons a {
  color: #555;
  font-size: 20px;
}


/* consultanat seaction  */
.consultant-section {
  position: relative;
  /* background-image: url('../../assets/img/projects/Background-2.jpg'); */
  background-color: var(--grey5-color);
  background-size: cover;
  background-position: center;
  padding: 100px 0;
  text-align: center;
  color: var(--black-color) !important;
}

/* .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 50, 0.562);
} */

.container {
  position: relative;
  z-index: 1;
  margin: 0 auto;
}

.section-header {
  line-height: 3rem;
  font-weight: 700;
  font-size: 55px;
  color: var(--black-color);
}

.section-text {
  font-size: 14px;
  line-height: 1.7em;
  font-weight: 500;
  -webkit-font-smoothing: antialiased;
  font-size: 18px;
  margin-bottom: 30px;
}

/* blog section start  */

.blog-section {
  background-color: var(--text-color);
  padding: 50px 0;
  text-align: center;
}

.mian-title {
  color: var(--black-color);
  font-size: 2em;
  line-height: 1em;
  font-weight: 500;
}

.main-header {
  color: var(--black-color);
  font-size: 3em;
  margin-bottom: 10px;
  line-height: 1em;
  font-weight: 500;
  padding-bottom: 10px;
}

.blogs-container {
  max-width: 1200px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.blog-cards {
  display: flex;
  justify-content: center;
}

.blog-card {
  width: 300px;
  border: 1px solid #ccc;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
}

.blog-thumbnail {
  position: relative;
}

.blog-thumbnail img {
  width: 100%;
  height: auto;
}

.date {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 5px 0px 0px 0px;
  background: #5B5F97;
  color: #fff;
  padding: 6px 20px;
  text-transform: uppercase;
  font-size: 12px;
  font-family: Lato;
  font-weight: 700;
  position: absolute;
  transition: .3s ease;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.42);
}

.blog-content {
  text-align: start;
  padding: 20px;
}

.blog-content h1 {
  padding: 0px;
  margin: 2px 0px;
  font-weight: 600;
}

.blog-title {
  margin: 0px;
  text-decoration: underline;
  font-size: 12px;
  color: blue;
}

.blog-text {
  font-size: 14px;
  color: #666;
  background-color: #fff;
  line-height: 1.7em;
  font-weight: 500;
}

.read-more-button {
  display: inline-block;
  background-color: #007bff;
  color: white;
  padding: 10px 20px;
  /* border-radius: 5px; */
  text-decoration: none;
  transition: background-color 0.3s ease;

  color: #5B5F97;
  background: rgba(91, 96, 151, 0.11);
  padding: 6px 20px;
  /* border-radius: 3px; */
  text-transform: uppercase;
  /* top: 15px; */
  position: relative;
  transition: .3s ease;
}

.read-more-button:hover {
  background-color: #5B5F97;
  color: white;
}


/* messge box  */
.message-sections {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  /* background-color: #f2f2f2; */
  text-align: center;

  position: relative;
  padding: 50px 0;
  /* background-image: url('../../assets/img/projects/Background\ 5.jpg'); */
  background-color: var(--text-color);

  background-size: cover;
  background-position: center;
}

/*
.message-sections::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 50, 0.514);
  z-index: 0;
} */

/* .container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
} */

.message-containers {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  z-index: 555;
}

.message-details {
  /* width: 45%; */
  width: 500px;
}

.message-details h3 {
  text-align: start;
  color: var(--black-color) !important;
  font-size: 3em;
  margin-bottom: 10px;
  line-height: 1em;
  font-weight: 500;
}

.message-details p {

  font-family: "Allerta", Helvetica, Arial, Lucida, sans-serif;
  color: #666;
  font-size: 1.2rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;
  text-align: start;
}

.send-message {
  width: 550px;
  margin: 30px 0px;
}

.send-message h3 {
  font-weight: 700;
  text-transform: uppercase;
  font-size: 26px;
  color: var(--black-color);
  letter-spacing: 2px;
  text-align: center;
  font-size: 2em;
  padding-bottom: 5px;
  line-height: 1em;
  font-weight: 500;
}

form {
  margin-top: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group input {
  border: none;
  border-bottom: 1px solid black;
  background-color: transparent;
  outline: none;
  color: var(--black-color);
  transition: background-color 0.3s;
  background-color: #dddddd;
  height: 50px;
  border-bottom-width: 2px;
  border-bottom-color: rgba(255, 255, 255, 0.35);
  padding-left: 10px;
}

.form-group input::placeholder {
  color: var(--black-color);
}

.error-message {
  text-align: start;
  color: red;
  margin-top: 5px;
  font-size: 14px;
}

.form-group input:focus {
  opacity: 0.7;
}

.full-width {
  width: 100%;
}

.HalfCont {
  display: flex;
  justify-content: space-between;
}

.half-width {
  width: 100%;
}

.half-cont {
  width: 45%;
}

.form-group textarea {
  width: 100%;
  height: 100px;
  border: none;
  border-bottom: 1px solid black;
  background-color: transparent;
  outline: none;
  color: white;
  transition: background-color 0.3s;
  background-color: #dddddd;
  border-bottom-width: 2px;
  border-bottom-color: rgba(255, 255, 255, 0.35);
  padding-left: 10px;
}

.form-group textarea::placeholder {
  color: var(--black-color);
}


.form-group textarea:focus {
  opacity: 0.7;
}

button {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: #0056b3;
}

.social-media-icons {
  text-align: start;
  margin-top: 20px;
}

.social-media-icons a {
  display: inline-block;
  margin-right: 20px;
  color: var(--dark-blue-color);
  font-size: 20px;

}

.social-media-icons a:last-child {
  margin-right: 0;
}

.address-info {
  display: flex;
  align-items: flex-start;
  margin: 20px 0px;
}

.address-info .adress {
  margin: 0;
  color: var(--black-color);
  padding-bottom: 5px;
  line-height: 1.1em;
  font-weight: 500;
}

.address-info .content-text {
  font-size: 14px;
  color: var(--dark-blue-color);
  line-height: 1.7em;
  font-weight: 500;
  -webkit-font-smoothing: antialiased;
}

.address-info p i {
  border-radius: 50%;
  overflow: hidden;
  padding: 10px;
  background-color: #ffffff;
  color: grey;
  margin-right: 10px;
  font-size: 25px;
}

.address-info div p {
  margin-bottom: 5px;
}



/* footer section */
footer {
  position: relative;
  background-color: var(--grey5-color);
  background-size: cover;
  padding: 20px;
  padding-top: 60px;
  padding-bottom: 20px;
}

.footer-content {
  display: flex;
  gap: 20px;
  justify-content: start;
  flex-wrap: wrap;
}

.footer-contact .address-info .adress {
  color: var(--black-color);
}

.footer-contact .address-info .content-text {
  color: var(--black-color);
}

.footer-logo img {
  width: 250px;
  padding: 0px 0px;
  margin-bottom: 10px;
}

.footer-nav,
.footer-contact {
  /* flex: 1; */
  margin-bottom: 20px;
}

.footer-nav h3 {
  font-weight: 700;
  font-size: 29px;
  color: var(--black-color);
}

.footer-contact h3 {
  font-weight: 700;
  font-size: 29px;
  color: var(--black-color);
}

.footer-contact p {
  font-weight: 700;
}

.footer-nav {
  width: 300px;
}

.footer-nav ul {
  list-style: none;
  padding: 0;
  text-align: start;

}

.footer-nav ul li {
  margin-bottom: 10px;
}

.footer-nav ul li a {
  color: var(--black-color);
  text-decoration: underline;
  letter-spacing: 1px;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: 0.05rem;
  text-decoration: none;
  margin-bottom: 20px;

}

.footer-nav ul li a:hover {
  text-decoration: underline;
  background-color: #e0e0e0;
}

.copyright-Section {
  text-align: center;
  font-family: Open Sans, Arial, sans-serif;
  font-size: 14px;
  color: var(--black-color);
  line-height: 1.7em;
  font-weight: 500;
  -webkit-font-smoothing: antialiased;
  font-size: 1.25rem;
  line-height: 1.7rem;
  letter-spacing: 0.05rem;

}

/* subscribe section s */
.subscribe-section {
  top: -95px;
  left: 300px;
  position: absolute;
  width: 800px;
  margin: auto;
  padding: 20px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  color: var(--black-color);
}

.subscribe-header {
  width: 400px;
}

.subscribe-header h2 {
  margin-top: 0;
}

.subscribe-content {
  width: 300px;
  margin-top: 20px;
}

.subscribe-content p {
  margin-bottom: 20px;
}

.subscribe-form input {
  font-size: 16px;
  color: #666;
  width: 100%;
  font-weight: 400;
  margin: 10px 0px;
  border: none;
  border-bottom: 2px solid #5B5F97;
  outline: none;
  /* Hide focus border */
}

.subscribe-form input:focus {
  outline: none;
  /* Hide focus border */
}

.subscribe-form input[type="text"],
.subscribe-form input[type="email"] {
  width: calc(50% - 5px);
  padding: 10px;
  margin-right: 5px;
}

.subscribe-form input[type="email"] {
  width: 100%;
  margin-right: 0;
}


/* Responsive desing  */

@media only screen and (max-width: 1080px) {

  .service-cards {
    justify-content: center;
  }
}

@media only screen and (max-width: 1080px) {

  .blogs-container {
    justify-content: center;
  }

  .message-containers {
    justify-content: center;

  }

  .message-details {
    width: 95%;
  }

  .resource-card {
    width: 48%;
  }

  .send-message {
    width: 95%;
  }
}

@media only screen and (max-width: 1025px) {
  .faq-question {
    width: 90% !important;
  }

  .about-info {
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .welcome-content p {
    max-width: 100%;
  }
}

@media only screen and (max-width: 850px) {
  .resource-card {
    width: 100%;
  }

  .ChatContainer {
    flex-wrap: wrap;
    margin-bottom: 20px;
  }

  .chat-container {
    margin: 0px;
  }
}

@media only screen and (max-width: 802px) {

  .about-info p {
    justify-content: space-between;
  }

  .service-card {
    width: 100%;
  }


}

@media only screen and (max-width: 665px) {
  .aboutContainer{
flex-wrap: wrap;
  }
}
@media only screen and (max-width: 650px) {
  .about-info p:first-of-type {
    padding-top: 10px;
  }

  .about-section {
    padding: 30px 0px;
  }

  .about-info p {
    /* padding: 25px 10px; */
  }

  .about-info p:nth-child(even) {
    margin-left: 0px !important;
  }

  .about-info p:nth-child(odd) {
    margin-right: 0px !important;
  }

  .row {
    justify-content: center;
  }

  .card {
    width: 100%;
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .blog-card {
    width: 90%;
    border: 1px solid #ccc;
    border-radius: 8px;
    overflow: hidden;
  }

  .send-message {
    width: 90%;
  }

  .welcome-content p {
    max-width: 100%;
  }

}

@media only screen and (max-width: 650px) {
  .chat-container {
    width: 92%;

  }
}


/* General styling for the message box, applies to both AI and user messages */
.message-contentBox {
  display: flex;
  /* Using flexbox for alignment */
  width: 90%;
  margin-bottom: 10px;
  /* Adds some space between messages */
}

/* Left-aligned boxes for AI messages */
.align-left {
  justify-content: flex-start;
  /* Aligns the box to the left */
}

/* Right-aligned boxes for User messages */
.align-right {
  justify-content: flex-end;
  /* Aligns the box to the right */
}


/* Optional: different background colors for AI and user messages */
.align-left .message-body {
  background-color: rgb(247, 248, 255);
  /* Light blue for AI messages */
}

.align-right .message-body {
  background-color: var(--dark-blue-color);
  /* Light green for user messages */
  color: white
}
