.cont {
  background-image: url('../../assets/img/projects/headerimg.jpeg');
  background-size: cover;
  background-position: center center;
  position: relative;
  /* background-color: rgba(143, 130, 130, 0.993); */


}

.cont::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.144);
  /* transform: rotate(-2deg); */

}

header {
  color: #fff;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-weight: bold;
  display: flex;
  align-items: center;
}

.logo i {
  font-size: 50px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-right: 10px;
  -webkit-text-fill-color: transparent;
  background: linear-gradient(to right, #ff5e62, #ff9966);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.logo span p {
  font-size: 12px;
  margin: 0px;
  color: #1b8ab5;
}

.logo span :nth-child(2) {
  margin: 0px;
  color: #6a1389;
}

nav {
  display: flex;
}

nav a {
  color: #ffffff;
  margin-left: 20px;
  font-weight: 700;
  text-decoration: none;
}

nav .requestBtn {
  background-color: #fff;
  color: #000000;
  padding: 3px 5px;
}

/* Message section s  */
.message-section {
  height: 300px;
  width: 100%;
  display: flex;
  align-items: flex-end;
  padding: 50px 40px;
}

.message-section p {
  margin: 10px 2px;
  font-size: 30px;
  line-height: 2rem;
  font-weight: 700;
  color: white;
}

button {
  padding: 10px 20px;
  background-color: #007bff;
  color: #fff;
  border: none;
  cursor: pointer;
}


/* service info   */
.service-info {
  padding: 20px;
  background: linear-gradient(to bottom, #570c4af6, #ce5dc8);
}

.skill-icon {
  width: 50px;
  height: 50px;
  background-color: #007bff;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: white;
  margin-bottom: 10px;
}

.service-info p {
  font-size: 18px;
  margin-left: 30px;
  line-height: 1.5;
  font-size: 16px;
  font-weight: 700;
  color: white;
}


/* our foccus */
.focusSections {
  padding: 20px;
  background-image: url('../../assets/img/projects/card\ imaes.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.contentBox {
  background-color: #5218bea1;
  color: white;
  border-radius: 16px 40px 16px 16px;
  margin: 20px;

}

.contentBox img {
  border-radius: 0px 40px 0px 0px;
}

.textBox h2 {
  font-size: 25px;
  font-weight: 700;
  letter-spacing: 3px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.textBox p {
  font-weight: 450;
}

/* request sections  */
.request-section {
  padding: 20px 40px;
  background-color: #5962af;
  text-align: center;
}

.request-content {
  position: relative;
}

.request-content img {
  position: absolute;
  top: -60px;
  left: 10%;
  transform: translateX(-50%);
  transition: opacity 0.3s ease;
  width: 150px;
  height: auto;
  border-radius: 50%;
}

.request-text {
  width: 70%;
  margin: auto;
  padding: 40px;
  display: flex;
  justify-content: space-between;



}
.request-text p {
  text-align: start;
  margin: 0px 10px;
  color: white;
  font-weight: 700;
  letter-spacing: 1px;
}

.request-button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  width: 400px;
}

/* services  */
.smart-services {
  padding: 20px;
  background-image: url('../../assets/img/projects/card\ imaes.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.smart-services-array {

}

/* Style for the technology list */
.technology-list {
  list-style-type: none;
  padding:  10px 20px;
  margin: 0px 50px;
}

.technology-list li {
  margin-bottom: 5px;
  font-weight: 700;
  color: white;
}


/* contact form   */
.contact-section {
  display: flex;
  justify-content: space-between;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.contact-info {

  flex: 1;
  margin-right: 20px;
}

.contact-form {
  flex: 1;
}

.contact-info img {
  width: 100px;
  border-radius: 50%;
}

.contact-info p {
  margin: 5px 0;
}

.contact-form input[type="text"],
.contact-form input[type="email"],
.contact-form textarea {
  width: 100%;
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-sizing: border-box;
}

.contact-form textarea {
  height: 150px;
}

.contact-form input[type="submit"] {
  background-color: #4CAF50;
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.contact-form input[type="submit"]:hover {
  background-color: #45a049;
}


/* footer section */
.footer-section {
  background-color: #333;
  color: #fff;
  padding: 20px;
  text-align: center;
  display: flex;

}

.footer-section img {
  width: 100px;
  border-radius: 50%;
}

.footer-section p {
  margin: 10px 0;
}

.contact-info {
  text-align: left;
}

.contact-info p {
  margin: 5px 0;
}
